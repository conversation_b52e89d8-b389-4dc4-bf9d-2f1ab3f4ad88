# 🛠️ Scripts di Gestione AWS RDS

Questa cartella contiene script Bash per automatizzare la gestione del database PostgreSQL su Amazon RDS.

## 📋 Prerequisiti

1. **AWS CLI configurata**:
   ```bash
   aws configure
   # Inserisci: Access Key, Secret Key, Region (eu-central-1), Output format (json)
   ```

2. **Permessi AWS necessari**:
   - `rds:*` (per gestire istanze RDS)
   - `ec2:*` (per gestire Security Groups)

3. **Strumenti richiesti**:
   - `bash` (disponibile su Linux/macOS/WSL)
   - `curl` (per rilevare IP pubblico)
   - `jq` (per parsing JSON - installabile con `apt install jq` o `brew install jq`)

## 🚀 Script Disponibili

### 1. `create_rds.sh` - Creazione Istanza RDS

**Scopo**: Crea una nuova istanza PostgreSQL 15 su AWS RDS con configurazione free-tier.

**Uso**:
```bash
export DB_PASSWORD="TuaPasswordSicura123!"
./scripts/create_rds.sh
```

**Variabili personalizzabili**:
```bash
export DB_INSTANCE_ID="mio-progetto-db"    # Nome istanza (default: rebuildlink-db)
export DB_NAME="mio_database"              # Nome database (default: rebuildlink)
export DB_USER="mio_utente"                # Username master (default: rebuildlink_user)
export AWS_REGION="eu-west-1"              # Regione AWS (default: eu-central-1)
```

**Cosa fa**:
- Rileva il tuo IP pubblico per la sicurezza
- Crea una Security Group che permette accesso solo dal tuo IP
- Provisiona istanza PostgreSQL 15 (db.t3.micro, 20GB, free-tier)
- Configura backup automatici (7 giorni)
- Opzionalmente attende che l'istanza sia pronta

### 2. `check_rds_status.sh` - Controllo Stato

**Scopo**: Monitora lo stato dell'istanza RDS e fornisce informazioni di connessione.

**Uso**:
```bash
./scripts/check_rds_status.sh

# Monitoraggio continuo (ogni 30 secondi)
watch -n 30 './scripts/check_rds_status.sh'
```

**Output tipico**:
```
🔍 Controllo stato dell'istanza RDS: rebuildlink-db

📊 Informazioni istanza:
   Status: available
   Engine: postgres 15.4
   Instance Class: db.t3.micro
   Storage: 20GB
   Database Name: rebuildlink
   Master User: rebuildlink_user
   Endpoint: rebuildlink-db.xyz.eu-central-1.rds.amazonaws.com:5432

✅ Istanza DISPONIBILE!

📝 DATABASE_URL di esempio:
   postgres://rebuildlink_user:<PASSWORD>@rebuildlink-db.xyz.eu-central-1.rds.amazonaws.com:5432/rebuildlink
```

### 3. `delete_rds.sh` - Eliminazione Istanza

**Scopo**: Elimina l'istanza RDS e le risorse associate per evitare costi.

⚠️ **ATTENZIONE**: Operazione irreversibile! Tutti i dati saranno persi.

**Uso**:
```bash
./scripts/delete_rds.sh
# Ti verrà chiesto di digitare 'DELETE' per confermare
```

**Cosa elimina**:
- Istanza RDS e tutti i suoi dati
- Backup automatici associati
- Security Group creata per l'istanza

## 🔧 Personalizzazione

Tutti gli script supportano variabili d'ambiente per la personalizzazione:

```bash
# Esempio: crea istanza per ambiente di staging
export DB_INSTANCE_ID="rebuildlink-staging"
export DB_NAME="rebuildlink_staging"
export DB_USER="staging_user"
export DB_PASSWORD="StagingPassword123!"
./scripts/create_rds.sh
```

## 🐛 Risoluzione Problemi

### Errore: "AWS CLI not configured"
```bash
aws configure
# Inserisci le tue credenziali AWS
```

### Errore: "jq: command not found"
```bash
# Ubuntu/Debian
sudo apt install jq

# macOS
brew install jq

# Windows (WSL)
sudo apt install jq
```

### Errore: "Access Denied"
Verifica che il tuo utente AWS abbia i permessi necessari per RDS ed EC2.

### Istanza bloccata in "creating"
È normale, il processo può richiedere 10-15 minuti. Usa `check_rds_status.sh` per monitorare.

## 💰 Gestione Costi

- **Free Tier**: db.t3.micro con 20GB è gratuito per 12 mesi
- **Backup**: 7 giorni di backup sono inclusi nel free tier
- **Eliminazione**: Usa sempre `delete_rds.sh` quando l'istanza non serve più

## 🔗 Link Utili

- [AWS RDS Free Tier](https://aws.amazon.com/rds/free/)
- [PostgreSQL su RDS](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_PostgreSQL.html)
- [AWS CLI Reference](https://docs.aws.amazon.com/cli/latest/reference/rds/)

---

**Tip**: Salva l'endpoint e le credenziali in un password manager per riferimenti futuri!
