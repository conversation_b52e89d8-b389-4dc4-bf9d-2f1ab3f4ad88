#!/usr/bin/env bash
# ============================================================
# Script: test_scripts.sh
# Scopo : Test di validazione per gli script RDS
# Autore: ReBuild Link Team
# ------------------------------------------------------------
# Questo script testa la sintassi e le dipendenze degli
# script RDS senza effettivamente creare risorse AWS.
#
# ============================================================

set -euo pipefail

echo "🧪 Test degli script RDS..."
echo ""

# -------------------------------
# 1. Test sintassi Bash
# -------------------------------
echo "1️⃣  Test sintassi Bash..."

scripts=("create_rds.sh" "check_rds_status.sh" "delete_rds.sh")

for script in "${scripts[@]}"; do
    if bash -n "scripts/$script"; then
        echo "   ✅ $script - sintassi OK"
    else
        echo "   ❌ $script - errore di sintassi"
        exit 1
    fi
done

echo ""

# -------------------------------
# 2. Test dipendenze
# -------------------------------
echo "2️⃣  Test dipendenze..."

dependencies=("curl" "aws" "jq")
missing_deps=()

for dep in "${dependencies[@]}"; do
    if command -v "$dep" >/dev/null 2>&1; then
        echo "   ✅ $dep - disponibile"
    else
        echo "   ⚠️  $dep - non trovato"
        missing_deps+=("$dep")
    fi
done

if [ ${#missing_deps[@]} -gt 0 ]; then
    echo ""
    echo "❌ Dipendenze mancanti: ${missing_deps[*]}"
    echo ""
    echo "📦 Installazione suggerita:"
    for dep in "${missing_deps[@]}"; do
        case "$dep" in
            "curl")
                echo "   sudo apt install curl  # Ubuntu/Debian"
                echo "   brew install curl      # macOS"
                ;;
            "aws")
                echo "   pip install awscli     # Python"
                echo "   brew install awscli    # macOS"
                ;;
            "jq")
                echo "   sudo apt install jq    # Ubuntu/Debian"
                echo "   brew install jq        # macOS"
                ;;
        esac
    done
    echo ""
    echo "⚠️  Gli script potrebbero non funzionare senza queste dipendenze."
else
    echo ""
    echo "✅ Tutte le dipendenze sono disponibili!"
fi

echo ""

# -------------------------------
# 3. Test configurazione AWS (opzionale)
# -------------------------------
echo "3️⃣  Test configurazione AWS..."

if command -v aws >/dev/null 2>&1; then
    if aws sts get-caller-identity >/dev/null 2>&1; then
        echo "   ✅ AWS CLI configurata correttamente"
        
        # Mostra informazioni account
        account_id=$(aws sts get-caller-identity --query Account --output text 2>/dev/null || echo "N/A")
        region=$(aws configure get region 2>/dev/null || echo "N/A")
        echo "   📋 Account ID: $account_id"
        echo "   🌍 Regione default: $region"
    else
        echo "   ⚠️  AWS CLI non configurata"
        echo "   💡 Esegui: aws configure"
    fi
else
    echo "   ⚠️  AWS CLI non installata"
fi

echo ""

# -------------------------------
# 4. Test variabili d'ambiente
# -------------------------------
echo "4️⃣  Test variabili d'ambiente..."

if [[ -n "${DB_PASSWORD:-}" ]]; then
    echo "   ✅ DB_PASSWORD impostata"
else
    echo "   ⚠️  DB_PASSWORD non impostata"
    echo "   💡 Esegui: export DB_PASSWORD='TuaPasswordSicura123!'"
fi

echo ""

# -------------------------------
# 5. Riepilogo
# -------------------------------
echo "📊 Riepilogo test:"
echo ""

if [ ${#missing_deps[@]} -eq 0 ]; then
    echo "✅ Sintassi script: OK"
    echo "✅ Dipendenze: OK"
    
    if command -v aws >/dev/null 2>&1 && aws sts get-caller-identity >/dev/null 2>&1; then
        echo "✅ AWS CLI: OK"
        echo ""
        echo "🎉 Tutti i test superati! Gli script sono pronti per l'uso."
        echo ""
        echo "🚀 Per creare un'istanza RDS:"
        echo "   export DB_PASSWORD='TuaPasswordSicura123!'"
        echo "   ./scripts/create_rds.sh"
    else
        echo "⚠️  AWS CLI: Non configurata"
        echo ""
        echo "⚡ Configura AWS CLI per usare gli script:"
        echo "   aws configure"
    fi
else
    echo "❌ Dipendenze: Mancanti (${missing_deps[*]})"
    echo ""
    echo "🔧 Installa le dipendenze mancanti prima di usare gli script."
fi

echo ""
echo "📚 Per maggiori informazioni: cat scripts/README.md"
